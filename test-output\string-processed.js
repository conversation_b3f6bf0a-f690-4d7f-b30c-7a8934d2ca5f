// Sample JavaScript code for testing string compression
// This file contains various types of strings that should be compressed

const greeting = "\u597D\u800C\u4E86\u4E86\u54E6\uFF0C\u3000\u4E07\u54E6\u4EBA\u4E86\u7684\uFF01";
const message = "\u5929\u548C\u4E00\u662F\u3000\u4E00\u662F\u3000\u554A\u3000\u4ED6\u800C\u662F\u4ED6\u3000\u6CA1\u800C\u662F\u662F\u554A\u4E2A\u800C";
const longString = "\u5929\u548C\u4E00\u662F\u3000\u4E00\u662F\u3000\u554A\u3000\u6CA1\u6211\u4ECE\u548C\u3000\u4E86\u54E6\u4F60\u4E2A\u800C\u4EBA\u3000\u662F\u4ED6\u4EBA\u4E00\u4F60\u4E2A\u3000\u4ED6\u548C\u554A\u4ED6\u3000\u662F\u548C\u54E6\u6211\u4E86\u7684\u3000\u7684\u800C\u53D1\u4E00\u4F60\u4E00\u4ED6\u800C\u4E86\u6709\u3000\u4E0D\u800C\u3000\u4ECE\u54E6\u6CA1\u7247\u4EBA\u800C\u662F\u662F\u800C\u7684\u3000\u4E0D\u800C\u4ECE\u554A\u6211\u662F\u800C\u3000\u4E00\u4ED6\u3000\u4ECE\u54E6\u4F60\u4ED6\u554A\u4E00\u4F60\u662F\u3000\u6CA1\u554A\u4F60\u6709\u3000\u4ECE\u548C\u554A\u4EBA\u554A\u4ECE\u4ED6\u800C\u4EBA\u662F";

// Object with string properties
const config = {
  "\u554A\u7247\u4E00\u7528\u4EBA\u4E86": "\u548C\u4ED6\u4ED6\u7247\u662F\uFF1A\uFF0F\uFF0F\u554A\u7247\u4E00\u3002\u800C\u4E0B\u554A\u6CA1\u7247\u4E86\u800C\u3002\u4ECE\u54E6\u6CA1",
  "\u4ED6\u4E00\u6CA1\u800C\u54E6\u6211\u4ED6": 5000,
  "\u4EBA\u800C\u4ED6\u4EBA\u4E00\u800C\u662F": 3,
  "\u548C\u800C\u554A\u7684\u800C\u4EBA\u662F": {
    "\u51FA\u54E6\u4F60\u4ED6\u800C\u4F60\u4ED6\uFF0D\u5929\u6709\u7247\u800C": "\u554A\u7247\u7247\u4E86\u4E00\u4ECE\u554A\u4ED6\u4E00\u54E6\u4F60\uFF0F\u5C31\u662F\u54E6\u4F60",
    "\u7231\u6211\u4ED6\u548C\u54E6\u4EBA\u4E00\u5728\u554A\u4ED6\u4E00\u54E6\u4F60": "\u767D\u800C\u554A\u4EBA\u800C\u4EBA\u3000\u4ED6\u54E6\u53EF\u800C\u4F60\u4E00\u4E8C\u4E09"
  }
};

// Function with string literals
function displayMessages() {
  console.log("\u4E09\u4ED6\u554A\u4EBA\u4ED6\u4E00\u4F60\u4E2A\u3000\u554A\u7247\u7247\u4E86\u4E00\u4ECE\u554A\u4ED6\u4E00\u54E6\u4F60\u3002\u3002\u3002");
  console.log("\u6765\u54E6\u554A\u7684\u4E00\u4F60\u4E2A\u3000\u4ECE\u54E6\u4F60\u53D1\u4E00\u4E2A\u6211\u4EBA\u554A\u4ED6\u4E00\u54E6\u4F60\u3002\u3002\u3002");
  console.log("\u51FA\u54E6\u4F60\u4F60\u800C\u4ECE\u4ED6\u4E00\u4F60\u4E2A\u3000\u4ED6\u54E6\u3000\u7231\u8DD1\u89C1\u3002\u3002\u3002");
  if (config.timeout > 3000) {
    console.log("\u7528\u662F\u4E00\u4F60\u4E2A\u3000\u800C\u4E0B\u4ED6\u800C\u4F60\u7684\u800C\u7684\u3000\u4ED6\u4E00\u6CA1\u800C\u54E6\u6211\u4ED6");
  }
  return "\u7231\u7247\u7247\u4E86\u4E00\u4ECE\u554A\u4ED6\u4E00\u54E6\u4F60\u3000\u662F\u4ED6\u554A\u4EBA\u4ED6\u800C\u7684\u3000\u662F\u6211\u4ECE\u4ECE\u800C\u662F\u662F\u53D1\u6211\u4E86\u4E86\u6709";
}

// Array of strings
const errorMessages = ["\u89C1\u4F60\u4E94\u554A\u4E86\u4E00\u7684\u3000\u4E00\u4F60\u7247\u6211\u4ED6\u3000\u7247\u4EBA\u54E6\u4E94\u4E00\u7684\u800C\u7684", "\u5E74\u800C\u4ED6\u4E3A\u54E6\u4EBA\u53EF\u3000\u4ECE\u54E6\u4F60\u4F60\u800C\u4ECE\u4ED6\u4E00\u54E6\u4F60\u3000\u53D1\u554A\u4E00\u4E86\u800C\u7684", "\u7231\u6211\u4ED6\u548C\u800C\u4F60\u4ED6\u4E00\u4ECE\u554A\u4ED6\u4E00\u54E6\u4F60\u3000\u4EBA\u800C\u53BB\u6211\u4E00\u4EBA\u800C\u7684", "\u8DD1\u800C\u4EBA\u6CA1\u4E00\u662F\u662F\u4E00\u54E6\u4F60\u3000\u7684\u800C\u4F60\u4E00\u800C\u7684", "\u5982\u800C\u662F\u54E6\u6211\u4EBA\u4ECE\u800C\u3000\u4F60\u54E6\u4ED6\u3000\u53D1\u54E6\u6211\u4F60\u7684"];

// Template literals (should be handled)
const templateMessage = `万而了从哦没而　${greeting}！　要哦我人　是而是是一哦你　为一了了　而下片一人而　一你　三零　没一你我他而是。`;

// Computed property access
const propertyName = "\u554A\u7247\u4E00\u7528\u4EBA\u4E86";
const url = config[propertyName];
const contentType = config["\u548C\u800C\u554A\u7684\u800C\u4EBA\u662F"]["\u51FA\u54E6\u4F60\u4ED6\u800C\u4F60\u4ED6\uFF0D\u5929\u6709\u7247\u800C"];

// Switch statement with string cases
function handleError(errorType) {
  switch (errorType) {
    case "\u4F60\u800C\u4ED6\u4E3A\u54E6\u4EBA\u53EF":
      return "\u8DD1\u4E86\u800C\u554A\u662F\u800C\u3000\u4ECE\u548C\u800C\u4ECE\u53EF\u3000\u6709\u54E6\u6211\u4EBA\u3000\u4E00\u4F60\u4ED6\u800C\u4EBA\u4F60\u800C\u4ED6\u3000\u4ECE\u54E6\u4F60\u4F60\u800C\u4ECE\u4ED6\u4E00\u54E6\u4F60";
    case "\u554A\u6211\u4ED6\u548C":
      return "\u8DD1\u4E86\u800C\u554A\u662F\u800C\u3000\u4E86\u54E6\u4E2A\u3000\u4E00\u4F60\u3000\u554A\u4E2A\u554A\u4E00\u4F60";
    case "\u7247\u800C\u4EBA\u6CA1\u4E00\u662F\u662F\u4E00\u54E6\u4F60":
      return "\u8981\u54E6\u6211\u3000\u7684\u54E6\u4F60'\u4ED6\u3000\u548C\u554A\u4E94\u800C\u3000\u7247\u800C\u4EBA\u6CA1\u4E00\u662F\u662F\u4E00\u54E6\u4F60\u3000\u4ED6\u54E6\u3000\u554A\u4ECE\u4ECE\u800C\u662F\u662F\u3000\u4ED6\u548C\u4E00\u662F\u3000\u4EBA\u800C\u662F\u54E6\u6211\u4EBA\u4ECE\u800C";
    default:
      return "\u7231\u4F60\u3000\u6211\u4F60\u53EF\u4F60\u54E6\u4E3A\u4F60\u3000\u800C\u4EBA\u4EBA\u54E6\u4EBA\u3000\u54E6\u4ECE\u4ECE\u6211\u4EBA\u4EBA\u800C\u7684";
  }
}

// Export for testing
if (typeof module !== "\u6211\u4F60\u7684\u800C\u53D1\u4E00\u4F60\u800C\u7684") {
  module.exports = {
    greeting,
    message,
    config,
    displayMessages,
    errorMessages,
    handleError
  };
}