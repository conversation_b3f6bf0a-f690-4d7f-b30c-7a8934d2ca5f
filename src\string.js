/**
 * String Processing Module
 * 
 * This module processes JavaScript code to find string literals and convert them
 * to Chinese characters/encoding before obfuscation. This runs first in the pipeline
 * to ensure proper handling by subsequent transformation steps.
 */

const parser = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const generate = require('@babel/generator').default;
const t = require('@babel/types');

/**
 * Convert a string to Chinese characters using Unicode encoding
 * @param {string} str - The string to convert
 * @returns {string} - String converted to Chinese characters
 */
function stringToChinese(str) {
    // Convert each character to its Unicode code point and map to Chinese characters
    // Using a range of common Chinese characters (U+4E00 to U+9FFF)
    const chineseBase = 0x4E00; // Start of CJK Unified Ideographs
    const chineseRange = 0x9FFF - 0x4E00; // Range size
    
    let result = '';
    for (let i = 0; i < str.length; i++) {
        const charCode = str.charCodeAt(i);
        // Map character code to Chinese character range
        const chineseCode = chineseBase + (charCode % chineseRange);
        result += String.fromCharCode(chineseCode);
    }
    
    return result;
}

/**
 * Alternative method: Convert string to Chinese using character mapping
 * @param {string} str - The string to convert
 * @returns {string} - String with Chinese character encoding
 */
function stringToChineseEncoded(str) {
    // Create a more deterministic mapping for better reversibility if needed
    const mapping = {
        'a': '啊', 'b': '不', 'c': '从', 'd': '的', 'e': '而', 'f': '发',
        'g': '个', 'h': '和', 'i': '一', 'j': '就', 'k': '可', 'l': '了',
        'm': '没', 'n': '你', 'o': '哦', 'p': '片', 'q': '去', 'r': '人',
        's': '是', 't': '他', 'u': '我', 'v': '五', 'w': '为', 'x': '下',
        'y': '有', 'z': '在', 'A': '爱', 'B': '白', 'C': '出', 'D': '大',
        'E': '二', 'F': '非', 'G': '高', 'H': '好', 'I': '见', 'J': '家',
        'K': '开', 'L': '来', 'M': '美', 'N': '年', 'O': '欧', 'P': '跑',
        'Q': '请', 'R': '如', 'S': '三', 'T': '天', 'U': '用', 'V': '很',
        'W': '万', 'X': '小', 'Y': '要', 'Z': '这', '0': '零', '1': '壹',
        '2': '贰', '3': '叁', '4': '肆', '5': '伍', '6': '陆', '7': '柒',
        '8': '捌', '9': '玖', ' ': '　', '.': '。', ',': '，', '!': '！',
        '?': '？', ':': '：', ';': '；', '"': '"', "'": "'", '(': '（',
        ')': '）', '[': '【', ']': '】', '{': '｛', '}': '｝', '-': '－',
        '_': '＿', '=': '＝', '+': '＋', '*': '＊', '/': '／', '\\': '＼',
        '|': '｜', '<': '＜', '>': '＞', '@': '＠', '#': '＃', '$': '＄',
        '%': '％', '^': '＾', '&': '＆', '~': '～', '`': '｀'
    };
    
    let result = '';
    for (let i = 0; i < str.length; i++) {
        const char = str[i];
        if (mapping[char]) {
            result += mapping[char];
        } else {
            // For unmapped characters, use Unicode offset method
            const charCode = str.charCodeAt(i);
            const chineseCode = 0x4E00 + (charCode % (0x9FFF - 0x4E00));
            result += String.fromCharCode(chineseCode);
        }
    }
    
    return result;
}

/**
 * Decode Chinese characters back to original string using Unicode offset method
 * @param {string} chineseStr - Chinese string to decode
 * @returns {string} - Decoded original string
 */
function chineseToString(chineseStr) {
    const chineseBase = 0x4E00; // Start of CJK Unified Ideographs
    const chineseRange = 0x9FFF - 0x4E00; // Range size

    let result = '';
    for (let i = 0; i < chineseStr.length; i++) {
        const chineseCode = chineseStr.charCodeAt(i);
        // Reverse the mapping: get original character code
        const originalCode = (chineseCode - chineseBase) % 256; // Assuming original chars were < 256
        result += String.fromCharCode(originalCode);
    }

    return result;
}

/**
 * Decode Chinese characters back to original string using character mapping
 * @param {string} chineseStr - Chinese string to decode
 * @returns {string} - Decoded original string
 */
function chineseEncodedToString(chineseStr) {
    // Reverse mapping from Chinese characters back to original characters
    const reverseMapping = {
        '啊': 'a', '不': 'b', '从': 'c', '的': 'd', '而': 'e', '发': 'f',
        '个': 'g', '和': 'h', '一': 'i', '就': 'j', '可': 'k', '了': 'l',
        '没': 'm', '你': 'n', '哦': 'o', '片': 'p', '去': 'q', '人': 'r',
        '是': 's', '他': 't', '我': 'u', '五': 'v', '为': 'w', '下': 'x',
        '有': 'y', '在': 'z', '爱': 'A', '白': 'B', '出': 'C', '大': 'D',
        '二': 'E', '非': 'F', '高': 'G', '好': 'H', '见': 'I', '家': 'J',
        '开': 'K', '来': 'L', '美': 'M', '年': 'N', '欧': 'O', '跑': 'P',
        '请': 'Q', '如': 'R', '三': 'S', '天': 'T', '用': 'U', '很': 'V',
        '万': 'W', '小': 'X', '要': 'Y', '这': 'Z', '零': '0', '壹': '1',
        '贰': '2', '叁': '3', '肆': '4', '伍': '5', '陆': '6', '柒': '7',
        '捌': '8', '玖': '9', '　': ' ', '。': '.', '，': ',', '！': '!',
        '？': '?', '：': ':', '；': ';', '"': '"', "'": "'", '（': '(',
        '）': ')', '【': '[', '】': ']', '｛': '{', '｝': '}', '－': '-',
        '＿': '_', '＝': '=', '＋': '+', '＊': '*', '／': '/', '＼': '\\',
        '｜': '|', '＜': '<', '＞': '>', '＠': '@', '＃': '#', '＄': '$',
        '％': '%', '＾': '^', '＆': '&', '～': '~', '｀': '`'
    };

    let result = '';
    for (let i = 0; i < chineseStr.length; i++) {
        const chineseChar = chineseStr[i];
        if (reverseMapping[chineseChar]) {
            result += reverseMapping[chineseChar];
        } else {
            // For unmapped characters, use Unicode offset method
            const chineseCode = chineseStr.charCodeAt(i);
            const originalCode = (chineseCode - 0x4E00) % 256;
            result += String.fromCharCode(originalCode);
        }
    }

    return result;
}

/**
 * Decode JavaScript code by converting Chinese string literals back to original strings
 * @param {string} code - JavaScript code with Chinese strings to decode
 * @param {Object} options - Decoding options
 * @returns {Object} - Result object with decoded code and statistics
 */
function processChineseToString(code, options = {}) {
    try {
        const ast = parser.parse(code, {
            sourceType: 'module',
            allowImportExportEverywhere: true,
            allowReturnOutsideFunction: true,
            plugins: [
                'jsx',
                'typescript',
                'decorators-legacy',
                'classProperties',
                'objectRestSpread',
                'functionBind',
                'exportDefaultFrom',
                'exportNamespaceFrom',
                'dynamicImport',
                'nullishCoalescingOperator',
                'optionalChaining'
            ]
        });

        let decodedCount = 0;
        const decodedStrings = [];
        const useEncodedMapping = options.useEncodedMapping !== false; // Default to true

        traverse(ast, {
            StringLiteral(path) {
                const chineseValue = path.node.value;

                // Check if this looks like a Chinese string (contains Chinese characters)
                if (/[\u4e00-\u9fff]/.test(chineseValue)) {
                    // Decode Chinese back to original
                    const originalValue = useEncodedMapping
                        ? chineseEncodedToString(chineseValue)
                        : chineseToString(chineseValue);

                    // Update the AST node
                    path.node.value = originalValue;

                    decodedCount++;
                    decodedStrings.push({
                        chinese: chineseValue,
                        original: originalValue,
                        length: originalValue.length
                    });
                }
            },

            TemplateLiteral(path) {
                // Also process template literal quasi (static parts)
                path.node.quasis.forEach((quasi, index) => {
                    if (quasi.value && quasi.value.raw && /[\u4e00-\u9fff]/.test(quasi.value.raw)) {
                        const chineseValue = quasi.value.raw;

                        const originalValue = useEncodedMapping
                            ? chineseEncodedToString(chineseValue)
                            : chineseToString(chineseValue);

                        quasi.value.raw = originalValue;
                        quasi.value.cooked = originalValue;

                        decodedCount++;
                        decodedStrings.push({
                            chinese: chineseValue,
                            original: originalValue,
                            length: originalValue.length,
                            type: 'template'
                        });
                    }
                });
            }
        });

        const decodedCode = generate(ast).code;

        return {
            code: decodedCode,
            success: true,
            decodedCount: decodedCount,
            decodedStrings: decodedStrings,
            originalSize: Buffer.byteLength(code, 'utf8'),
            decodedSize: Buffer.byteLength(decodedCode, 'utf8'),
            metadata: {
                engine: 'chinese-to-string',
                method: useEncodedMapping ? 'encoded-mapping' : 'unicode-offset',
                totalStringsDecoded: decodedCount,
                averageStringLength: decodedStrings.length > 0
                    ? decodedStrings.reduce((sum, s) => sum + s.length, 0) / decodedStrings.length
                    : 0
            }
        };

    } catch (error) {
        console.error('Chinese decoding error:', error);

        return {
            code: code, // Return original code on failure
            success: false,
            error: error.message,
            decodedCount: 0,
            decodedStrings: [],
            originalSize: Buffer.byteLength(code, 'utf8'),
            decodedSize: Buffer.byteLength(code, 'utf8'),
            metadata: {
                engine: 'chinese-to-string',
                error: error.message
            }
        };
    }
}

/**
 * Process JavaScript code to find and convert string literals to Chinese
 * @param {string} code - JavaScript code to process
 * @param {Object} options - Processing options
 * @returns {Object} - Result object with processed code and statistics
 */
function processStringToChinese(code, options = {}) {
    try {
        const ast = parser.parse(code, {
            sourceType: 'module',
            allowImportExportEverywhere: true,
            allowReturnOutsideFunction: true,
            plugins: [
                'jsx',
                'typescript',
                'decorators-legacy',
                'classProperties',
                'objectRestSpread',
                'functionBind',
                'exportDefaultFrom',
                'exportNamespaceFrom',
                'dynamicImport',
                'nullishCoalescingOperator',
                'optionalChaining'
            ]
        });

        let processedCount = 0;
        const processedStrings = [];
        const useEncodedMapping = options.useEncodedMapping !== false; // Default to true

        traverse(ast, {
            StringLiteral(path) {
                const originalValue = path.node.value;
                
                // Skip empty strings or very short strings that might be important
                if (originalValue.length === 0 || (originalValue.length === 1 && /[{}()\[\];,.]/.test(originalValue))) {
                    return;
                }
                
                // Convert string to Chinese
                const chineseValue = useEncodedMapping 
                    ? stringToChineseEncoded(originalValue)
                    : stringToChinese(originalValue);
                
                // Update the AST node
                path.node.value = chineseValue;
                
                processedCount++;
                processedStrings.push({
                    original: originalValue,
                    chinese: chineseValue,
                    length: originalValue.length
                });
            },
            
            TemplateLiteral(path) {
                // Also process template literal quasi (static parts)
                path.node.quasis.forEach((quasi, index) => {
                    if (quasi.value && quasi.value.raw) {
                        const originalValue = quasi.value.raw;
                        
                        if (originalValue.length > 0) {
                            const chineseValue = useEncodedMapping 
                                ? stringToChineseEncoded(originalValue)
                                : stringToChinese(originalValue);
                            
                            quasi.value.raw = chineseValue;
                            quasi.value.cooked = chineseValue;
                            
                            processedCount++;
                            processedStrings.push({
                                original: originalValue,
                                chinese: chineseValue,
                                length: originalValue.length,
                                type: 'template'
                            });
                        }
                    }
                });
            }
        });

        const processedCode = generate(ast).code;

        return {
            code: processedCode,
            success: true,
            processedCount: processedCount,
            processedStrings: processedStrings,
            originalSize: Buffer.byteLength(code, 'utf8'),
            processedSize: Buffer.byteLength(processedCode, 'utf8'),
            metadata: {
                engine: 'string-to-chinese',
                method: useEncodedMapping ? 'encoded-mapping' : 'unicode-offset',
                totalStringsFound: processedCount,
                averageStringLength: processedStrings.length > 0 
                    ? processedStrings.reduce((sum, s) => sum + s.length, 0) / processedStrings.length 
                    : 0
            }
        };

    } catch (error) {
        console.error('String processing error:', error);
        
        return {
            code: code, // Return original code on failure
            success: false,
            error: error.message,
            processedCount: 0,
            processedStrings: [],
            originalSize: Buffer.byteLength(code, 'utf8'),
            processedSize: Buffer.byteLength(code, 'utf8'),
            metadata: {
                engine: 'string-to-chinese',
                error: error.message
            }
        };
    }
}

module.exports = {
    processStringToChinese,
    stringToChinese,
    stringToChineseEncoded,
    processChineseToString,
    chineseToString,
    chineseEncodedToString
};
