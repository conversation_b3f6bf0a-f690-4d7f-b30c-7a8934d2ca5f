/**
 * String Processing Module
 * 
 * This module processes JavaScript code to find string literals and convert them
 * to Chinese characters/encoding before obfuscation. This runs first in the pipeline
 * to ensure proper handling by subsequent transformation steps.
 */

const parser = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const generate = require('@babel/generator').default;
const t = require('@babel/types');

/**
 * Convert a string to Chinese characters using Unicode encoding
 * @param {string} str - The string to convert
 * @returns {string} - String converted to Chinese characters
 */
function stringToChinese(str) {
    // Convert each character to its Unicode code point and map to Chinese characters
    // Using a range of common Chinese characters (U+4E00 to U+9FFF)
    const chineseBase = 0x4E00; // Start of CJK Unified Ideographs
    const chineseRange = 0x9FFF - 0x4E00; // Range size
    
    let result = '';
    for (let i = 0; i < str.length; i++) {
        const charCode = str.charCodeAt(i);
        // Map character code to Chinese character range
        const chineseCode = chineseBase + (charCode % chineseRange);
        result += String.fromCharCode(chineseCode);
    }
    
    return result;
}

/**
 * Alternative method: Convert string to Chinese using character mapping
 * @param {string} str - The string to convert
 * @returns {string} - String with Chinese character encoding
 */
function stringToChineseEncoded(str) {
    // Create a more deterministic mapping for better reversibility if needed
    const mapping = {
        'a': '啊', 'b': '不', 'c': '从', 'd': '的', 'e': '而', 'f': '发',
        'g': '个', 'h': '和', 'i': '一', 'j': '就', 'k': '可', 'l': '了',
        'm': '没', 'n': '你', 'o': '哦', 'p': '片', 'q': '去', 'r': '人',
        's': '是', 't': '他', 'u': '我', 'v': '五', 'w': '为', 'x': '下',
        'y': '有', 'z': '在', 'A': '爱', 'B': '白', 'C': '出', 'D': '大',
        'E': '二', 'F': '非', 'G': '高', 'H': '好', 'I': '见', 'J': '家',
        'K': '开', 'L': '来', 'M': '美', 'N': '年', 'O': '哦', 'P': '跑',
        'Q': '请', 'R': '如', 'S': '三', 'T': '天', 'U': '用', 'V': '很',
        'W': '万', 'X': '小', 'Y': '要', 'Z': '这', '0': '零', '1': '一',
        '2': '二', '3': '三', '4': '四', '5': '五', '6': '六', '7': '七',
        '8': '八', '9': '九', ' ': '　', '.': '。', ',': '，', '!': '！',
        '?': '？', ':': '：', ';': '；', '"': '"', "'": "'", '(': '（',
        ')': '）', '[': '【', ']': '】', '{': '｛', '}': '｝', '-': '－',
        '_': '＿', '=': '＝', '+': '＋', '*': '＊', '/': '／', '\\': '＼',
        '|': '｜', '<': '＜', '>': '＞', '@': '＠', '#': '＃', '$': '＄',
        '%': '％', '^': '＾', '&': '＆', '~': '～', '`': '｀'
    };
    
    let result = '';
    for (let i = 0; i < str.length; i++) {
        const char = str[i];
        if (mapping[char]) {
            result += mapping[char];
        } else {
            // For unmapped characters, use Unicode offset method
            const charCode = str.charCodeAt(i);
            const chineseCode = 0x4E00 + (charCode % (0x9FFF - 0x4E00));
            result += String.fromCharCode(chineseCode);
        }
    }
    
    return result;
}

/**
 * Process JavaScript code to find and convert string literals to Chinese
 * @param {string} code - JavaScript code to process
 * @param {Object} options - Processing options
 * @returns {Object} - Result object with processed code and statistics
 */
function processStringToChinese(code, options = {}) {
    try {
        const ast = parser.parse(code, {
            sourceType: 'module',
            allowImportExportEverywhere: true,
            allowReturnOutsideFunction: true,
            plugins: [
                'jsx',
                'typescript',
                'decorators-legacy',
                'classProperties',
                'objectRestSpread',
                'functionBind',
                'exportDefaultFrom',
                'exportNamespaceFrom',
                'dynamicImport',
                'nullishCoalescingOperator',
                'optionalChaining'
            ]
        });

        let processedCount = 0;
        const processedStrings = [];
        const useEncodedMapping = options.useEncodedMapping !== false; // Default to true

        traverse(ast, {
            StringLiteral(path) {
                const originalValue = path.node.value;
                
                // Skip empty strings or very short strings that might be important
                if (originalValue.length === 0 || (originalValue.length === 1 && /[{}()\[\];,.]/.test(originalValue))) {
                    return;
                }
                
                // Convert string to Chinese
                const chineseValue = useEncodedMapping 
                    ? stringToChineseEncoded(originalValue)
                    : stringToChinese(originalValue);
                
                // Update the AST node
                path.node.value = chineseValue;
                
                processedCount++;
                processedStrings.push({
                    original: originalValue,
                    chinese: chineseValue,
                    length: originalValue.length
                });
            },
            
            TemplateLiteral(path) {
                // Also process template literal quasi (static parts)
                path.node.quasis.forEach((quasi, index) => {
                    if (quasi.value && quasi.value.raw) {
                        const originalValue = quasi.value.raw;
                        
                        if (originalValue.length > 0) {
                            const chineseValue = useEncodedMapping 
                                ? stringToChineseEncoded(originalValue)
                                : stringToChinese(originalValue);
                            
                            quasi.value.raw = chineseValue;
                            quasi.value.cooked = chineseValue;
                            
                            processedCount++;
                            processedStrings.push({
                                original: originalValue,
                                chinese: chineseValue,
                                length: originalValue.length,
                                type: 'template'
                            });
                        }
                    }
                });
            }
        });

        const processedCode = generate(ast).code;

        return {
            code: processedCode,
            success: true,
            processedCount: processedCount,
            processedStrings: processedStrings,
            originalSize: Buffer.byteLength(code, 'utf8'),
            processedSize: Buffer.byteLength(processedCode, 'utf8'),
            metadata: {
                engine: 'string-to-chinese',
                method: useEncodedMapping ? 'encoded-mapping' : 'unicode-offset',
                totalStringsFound: processedCount,
                averageStringLength: processedStrings.length > 0 
                    ? processedStrings.reduce((sum, s) => sum + s.length, 0) / processedStrings.length 
                    : 0
            }
        };

    } catch (error) {
        console.error('String processing error:', error);
        
        return {
            code: code, // Return original code on failure
            success: false,
            error: error.message,
            processedCount: 0,
            processedStrings: [],
            originalSize: Buffer.byteLength(code, 'utf8'),
            processedSize: Buffer.byteLength(code, 'utf8'),
            metadata: {
                engine: 'string-to-chinese',
                error: error.message
            }
        };
    }
}

module.exports = {
    processStringToChinese,
    stringToChinese,
    stringToChineseEncoded
};
