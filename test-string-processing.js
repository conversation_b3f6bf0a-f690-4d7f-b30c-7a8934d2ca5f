/**
 * Test script for string processing functionality
 */

const fs = require('fs');
const path = require('path');
const { processStringToChinese } = require('./src/string');
const { executeProcessesInOrder } = require('./src/order');

async function testStringProcessing() {
    console.log('=== Testing String Processing Functionality ===\n');
    
    try {
        // Read test sample
        const samplePath = path.join(__dirname, 'test-samples', 'sample1.js');
        const originalCode = fs.readFileSync(samplePath, 'utf8');
        
        console.log('Original code sample (first 200 chars):');
        console.log(originalCode.substring(0, 200) + '...\n');
        
        // Test string processing only
        console.log('--- Testing String Processing Only ---');
        const stringResult = await processStringToChinese(originalCode);
        
        if (stringResult.success) {
            console.log('✅ String processing successful!');
            console.log(`📊 Processed ${stringResult.processedCount} strings`);
            console.log(`📏 Original size: ${stringResult.originalSize} bytes`);
            console.log(`📏 Processed size: ${stringResult.processedSize} bytes`);
            console.log(`🔧 Method: ${stringResult.metadata.method}`);
            
            console.log('\nSample string conversions:');
            stringResult.processedStrings.slice(0, 5).forEach((str, index) => {
                console.log(`  ${index + 1}. "${str.original}" → "${str.chinese}"`);
            });
            
            console.log('\nProcessed code sample (first 300 chars):');
            console.log(stringResult.code.substring(0, 300) + '...\n');
        } else {
            console.log('❌ String processing failed:', stringResult.error);
            return;
        }
        
        // Test full pipeline
        console.log('--- Testing Full Pipeline (String → Simplify → Properties → Global → JS-Confuser) ---');
        const fullResult = await executeProcessesInOrder(originalCode);
        
        if (fullResult.code) {
            console.log('✅ Full pipeline successful!');
            console.log(`📊 Total processes: ${fullResult.totalProcesses}`);
            console.log(`✅ Successful processes: ${fullResult.successfulProcesses}`);
            
            console.log('\nProcess execution details:');
            fullResult.executionDetails.forEach((detail, index) => {
                const status = detail.success ? '✅' : '❌';
                console.log(`  ${index + 1}. ${status} ${detail.processName} (${detail.duration}ms)`);
                if (detail.processedCount !== undefined) {
                    console.log(`     - Processed items: ${detail.processedCount}`);
                }
                if (detail.error) {
                    console.log(`     - Error: ${detail.error}`);
                }
            });
            
            console.log('\nFinal obfuscated code sample (first 200 chars):');
            console.log(fullResult.code.substring(0, 200) + '...\n');
            
            // Save results for inspection
            const outputDir = path.join(__dirname, 'test-output');
            if (!fs.existsSync(outputDir)) {
                fs.mkdirSync(outputDir);
            }
            
            fs.writeFileSync(path.join(outputDir, 'string-processed.js'), stringResult.code);
            fs.writeFileSync(path.join(outputDir, 'fully-obfuscated.js'), fullResult.code);
            
            console.log('📁 Results saved to test-output/ directory');
            console.log('   - string-processed.js (after string processing only)');
            console.log('   - fully-obfuscated.js (after full pipeline)');
            
        } else {
            console.log('❌ Full pipeline failed');
            fullResult.executionDetails.forEach((detail) => {
                if (!detail.success) {
                    console.log(`   - ${detail.processName}: ${detail.error}`);
                }
            });
        }
        
    } catch (error) {
        console.error('❌ Test failed with error:', error);
    }
}

// Run the test
testStringProcessing().then(() => {
    console.log('\n=== Test Complete ===');
}).catch(error => {
    console.error('Test execution failed:', error);
});
