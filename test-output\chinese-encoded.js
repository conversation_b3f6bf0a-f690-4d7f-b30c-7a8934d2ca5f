const greeting = "\u597D\u800C\u4E86\u4E86\u54E6\uFF0C\u3000\u4E07\u54E6\u4EBA\u4E86\u7684\uFF01";
const message = "\u5929\u548C\u4E00\u662F\u3000\u4E00\u662F\u3000\u554A\u3000\u4ED6\u800C\u662F\u4ED6\u3000\u6CA1\u800C\u662F\u662F\u554A\u4E2A\u800C";
const config = {
  "\u554A\u7247\u4E00\u7528\u4EBA\u4E86": "\u548C\u4ED6\u4ED6\u7247\u662F\uFF1A\uFF0F\uFF0F\u554A\u7247\u4E00\u3002\u800C\u4E0B\u554A\u6CA1\u7247\u4E86\u800C\u3002\u4ECE\u54E6\u6CA1",
  "\u4ED6\u4E00\u6CA1\u800C\u54E6\u6211\u4ED6": 5000
};
function displayMessage() {
  console.log("\u4E09\u4ED6\u554A\u4EBA\u4ED6\u4E00\u4F60\u4E2A\u3000\u554A\u7247\u7247\u4E86\u4E00\u4ECE\u554A\u4ED6\u4E00\u54E6\u4F60\u3002\u3002\u3002");
  return "\u7231\u7247\u7247\u4E86\u4E00\u4ECE\u554A\u4ED6\u4E00\u54E6\u4F60\u3000\u662F\u4ED6\u554A\u4EBA\u4ED6\u800C\u7684\u3000\u662F\u6211\u4ECE\u4ECE\u800C\u662F\u662F\u53D1\u6211\u4E86\u4E86\u6709";
}
const templateMessage = `万而了从哦没而　${greeting}！　要哦我人　是而是是一哦你　为一了了　而下片一人而。`;