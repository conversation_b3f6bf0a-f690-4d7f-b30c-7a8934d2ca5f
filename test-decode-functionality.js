/**
 * Test script for Chinese string decoding functionality
 */

const fs = require('fs');
const path = require('path');
const { 
    processStringToChinese, 
    processChineseToString,
    stringToChinese,
    stringToChineseEncoded,
    chineseToString,
    chineseEncodedToString
} = require('./src/string');

async function testDecodefunctionality() {
    console.log('=== Testing Chinese String Decode Functionality ===\n');
    
    try {
        // Test 1: Basic string encoding and decoding
        console.log('--- Test 1: Basic String Encoding/Decoding ---');
        const testStrings = [
            'Hello, World!',
            'This is a test',
            'JavaScript obfuscation',
            'console.log',
            'function test() { return "success"; }'
        ];
        
        console.log('Testing individual string conversions:');
        testStrings.forEach((str, index) => {
            const encoded = stringToChineseEncoded(str);
            const decoded = chineseEncodedToString(encoded);
            const success = str === decoded ? '✅' : '❌';
            
            console.log(`  ${index + 1}. ${success} "${str}"`);
            console.log(`     → Encoded: "${encoded}"`);
            console.log(`     → Decoded: "${decoded}"`);
            console.log(`     → Match: ${str === decoded}\n`);
        });
        
        // Test 2: Full code encoding and decoding
        console.log('--- Test 2: Full Code Encoding/Decoding ---');
        const originalCode = `
const greeting = "Hello, World!";
const message = "This is a test message";
const config = {
    "apiUrl": "https://api.example.com",
    "timeout": 5000
};

function displayMessage() {
    console.log("Starting application...");
    return "Application started successfully";
}

const templateMessage = \`Welcome \${greeting}! Your session will expire.\`;
        `.trim();
        
        console.log('Original code:');
        console.log(originalCode.substring(0, 200) + '...\n');
        
        // Encode to Chinese
        const encodeResult = await processStringToChinese(originalCode);
        if (encodeResult.success) {
            console.log('✅ Encoding successful!');
            console.log(`📊 Encoded ${encodeResult.processedCount} strings`);
            console.log('\nEncoded code sample:');
            console.log(encodeResult.code.substring(0, 300) + '...\n');
            
            // Decode back to original
            const decodeResult = await processChineseToString(encodeResult.code);
            if (decodeResult.success) {
                console.log('✅ Decoding successful!');
                console.log(`📊 Decoded ${decodeResult.decodedCount} strings`);
                console.log('\nDecoded code:');
                console.log(decodeResult.code.substring(0, 200) + '...\n');
                
                // Check if round-trip is successful
                const isRoundTripSuccessful = originalCode.trim() === decodeResult.code.trim();
                console.log(`🔄 Round-trip test: ${isRoundTripSuccessful ? '✅ SUCCESS' : '❌ FAILED'}`);
                
                if (!isRoundTripSuccessful) {
                    console.log('\n--- Debugging Round-trip Differences ---');
                    console.log('Original length:', originalCode.length);
                    console.log('Decoded length:', decodeResult.code.length);
                    
                    // Show first difference
                    for (let i = 0; i < Math.min(originalCode.length, decodeResult.code.length); i++) {
                        if (originalCode[i] !== decodeResult.code[i]) {
                            console.log(`First difference at position ${i}:`);
                            console.log(`  Original: "${originalCode[i]}" (${originalCode.charCodeAt(i)})`);
                            console.log(`  Decoded:  "${decodeResult.code[i]}" (${decodeResult.code.charCodeAt(i)})`);
                            break;
                        }
                    }
                }
                
                // Save results for inspection
                const outputDir = path.join(__dirname, 'test-output');
                if (!fs.existsSync(outputDir)) {
                    fs.mkdirSync(outputDir);
                }
                
                fs.writeFileSync(path.join(outputDir, 'original-code.js'), originalCode);
                fs.writeFileSync(path.join(outputDir, 'chinese-encoded.js'), encodeResult.code);
                fs.writeFileSync(path.join(outputDir, 'decoded-code.js'), decodeResult.code);
                
                console.log('\n📁 Test results saved to test-output/ directory:');
                console.log('   - original-code.js (original input)');
                console.log('   - chinese-encoded.js (after encoding to Chinese)');
                console.log('   - decoded-code.js (after decoding back)');
                
            } else {
                console.log('❌ Decoding failed:', decodeResult.error);
            }
        } else {
            console.log('❌ Encoding failed:', encodeResult.error);
        }
        
        // Test 3: Test with existing Chinese-encoded file
        console.log('\n--- Test 3: Decoding Existing Chinese-Encoded File ---');
        const outputDir = path.join(__dirname, 'test-output');
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir);
        }

        const chineseEncodedPath = path.join(__dirname, 'test-output', 'string-processed.js');
        if (fs.existsSync(chineseEncodedPath)) {
            const chineseEncodedCode = fs.readFileSync(chineseEncodedPath, 'utf8');
            console.log('Found existing Chinese-encoded file, attempting to decode...');

            const decodeExistingResult = await processChineseToString(chineseEncodedCode);
            if (decodeExistingResult.success) {
                console.log('✅ Successfully decoded existing file!');
                console.log(`📊 Decoded ${decodeExistingResult.decodedCount} strings`);

                fs.writeFileSync(path.join(outputDir, 'decoded-existing.js'), decodeExistingResult.code);
                console.log('📁 Saved as decoded-existing.js');
                
                console.log('\nSample decoded strings:');
                decodeExistingResult.decodedStrings.slice(0, 3).forEach((str, index) => {
                    console.log(`  ${index + 1}. "${str.chinese}" → "${str.original}"`);
                });
            } else {
                console.log('❌ Failed to decode existing file:', decodeExistingResult.error);
            }
        } else {
            console.log('No existing Chinese-encoded file found. Run test-string-processing.js first.');
        }
        
    } catch (error) {
        console.error('❌ Test failed with error:', error);
    }
}

// Run the test
testDecodefunctionality().then(() => {
    console.log('\n=== Decode Test Complete ===');
}).catch(error => {
    console.error('Test execution failed:', error);
});
