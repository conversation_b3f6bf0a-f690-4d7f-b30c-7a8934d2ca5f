#!/usr/bin/env node

/**
 * Chinese String Decoder Utility
 * 
 * This utility can decode JavaScript files that have been processed with Chinese string encoding.
 * It converts Chinese characters back to their original string literals.
 * 
 * Usage:
 *   node decode-chinese-strings.js <input-file> [output-file]
 *   node decode-chinese-strings.js input.js output.js
 *   node decode-chinese-strings.js input.js (outputs to input-decoded.js)
 */

const fs = require('fs');
const path = require('path');
const { processChineseToString, chineseEncodedToString } = require('./src/string');

function showUsage() {
    console.log('Chinese String Decoder Utility');
    console.log('');
    console.log('Usage:');
    console.log('  node decode-chinese-strings.js <input-file> [output-file]');
    console.log('');
    console.log('Examples:');
    console.log('  node decode-chinese-strings.js obfuscated.js decoded.js');
    console.log('  node decode-chinese-strings.js obfuscated.js');
    console.log('  node decode-chinese-strings.js test-output/string-processed.js');
    console.log('');
    console.log('Options:');
    console.log('  --help, -h    Show this help message');
    console.log('  --test        Test decode functionality with sample strings');
}

function testDecodeFunctionality() {
    console.log('=== Testing Chinese String Decode Functionality ===\n');
    
    const testStrings = [
        'Hello, World!',
        'console.log',
        'function test() { return "success"; }',
        'https://api.example.com',
        'JavaScript obfuscation'
    ];
    
    console.log('Testing string encoding/decoding:');
    let allPassed = true;
    
    testStrings.forEach((original, index) => {
        const encoded = require('./src/string').stringToChineseEncoded(original);
        const decoded = chineseEncodedToString(encoded);
        const passed = original === decoded;
        allPassed = allPassed && passed;
        
        console.log(`  ${index + 1}. ${passed ? '✅' : '❌'} "${original}"`);
        console.log(`     → Encoded: "${encoded}"`);
        console.log(`     → Decoded: "${decoded}"`);
        if (!passed) {
            console.log(`     → ❌ MISMATCH!`);
        }
        console.log('');
    });
    
    console.log(`Overall result: ${allPassed ? '✅ All tests passed!' : '❌ Some tests failed!'}`);
    return allPassed;
}

async function decodeFile(inputFile, outputFile) {
    try {
        // Check if input file exists
        if (!fs.existsSync(inputFile)) {
            console.error(`❌ Error: Input file "${inputFile}" does not exist.`);
            return false;
        }
        
        // Read input file
        console.log(`📖 Reading file: ${inputFile}`);
        const inputCode = fs.readFileSync(inputFile, 'utf8');
        
        // Check if file contains Chinese characters
        const hasChinese = /[\u4e00-\u9fff]/.test(inputCode);
        if (!hasChinese) {
            console.log('⚠️  Warning: Input file does not appear to contain Chinese characters.');
            console.log('   This file may not have been processed with Chinese string encoding.');
        }
        
        console.log(`📊 Input file size: ${inputCode.length} characters`);
        
        // Decode the file
        console.log('🔄 Decoding Chinese strings...');
        const result = await processChineseToString(inputCode);
        
        if (result.success) {
            console.log('✅ Decoding successful!');
            console.log(`📊 Decoded ${result.decodedCount} Chinese strings`);
            console.log(`📏 Output size: ${result.code.length} characters`);
            
            // Write output file
            fs.writeFileSync(outputFile, result.code, 'utf8');
            console.log(`💾 Decoded file saved as: ${outputFile}`);
            
            // Show sample decoded strings
            if (result.decodedStrings.length > 0) {
                console.log('\n📝 Sample decoded strings:');
                result.decodedStrings.slice(0, 5).forEach((str, index) => {
                    const preview = str.original.length > 50 
                        ? str.original.substring(0, 50) + '...'
                        : str.original;
                    console.log(`  ${index + 1}. "${preview}"`);
                });
                
                if (result.decodedStrings.length > 5) {
                    console.log(`  ... and ${result.decodedStrings.length - 5} more strings`);
                }
            }
            
            return true;
        } else {
            console.error('❌ Decoding failed:', result.error);
            return false;
        }
        
    } catch (error) {
        console.error('❌ Error processing file:', error.message);
        return false;
    }
}

async function main() {
    const args = process.argv.slice(2);
    
    // Handle help flag
    if (args.includes('--help') || args.includes('-h')) {
        showUsage();
        return;
    }
    
    // Handle test flag
    if (args.includes('--test')) {
        testDecodeFunctionality();
        return;
    }
    
    // Check arguments
    if (args.length === 0) {
        console.error('❌ Error: No input file specified.');
        console.log('');
        showUsage();
        process.exit(1);
    }
    
    const inputFile = args[0];
    let outputFile = args[1];
    
    // Generate output filename if not provided
    if (!outputFile) {
        const parsed = path.parse(inputFile);
        outputFile = path.join(parsed.dir, `${parsed.name}-decoded${parsed.ext}`);
    }
    
    console.log('🚀 Chinese String Decoder');
    console.log(`📁 Input:  ${inputFile}`);
    console.log(`📁 Output: ${outputFile}`);
    console.log('');
    
    const success = await decodeFile(inputFile, outputFile);
    
    if (success) {
        console.log('\n🎉 Decoding completed successfully!');
        process.exit(0);
    } else {
        console.log('\n💥 Decoding failed!');
        process.exit(1);
    }
}

// Run the utility
main().catch(error => {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
});
